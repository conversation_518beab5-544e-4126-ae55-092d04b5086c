<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Berita;
use App\Models\Prestasi;
use App\Models\User;
use App\Models\Kegiatan;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class AdminController extends Controller
{
    public function index()
    {
        return Inertia::render('dashboard', [
            'jumlah_berita' => Berita::count(),
            'jumlah_prestasi' => Prestasi::count(),
            'jumlah_user' => User::count(),
        ]);
    }

    public function kegiatan()
    {
        $kegiatan = Kegiatan::with('user')->latest()->get()->map(function ($item) {
            return [
                'id' => $item->id,
                'nama_kegiatan' => $item->judul_kegiatan,
                'deskripsi' => $item->deskripsi_kegiatan,
                'tanggal_kegiatan' => Carbon::parse($item->tanggal_kegiatan)->format('Y-m-d'),
                'jenis_kegiatan' => $item->jenis_kegiatan,
                'foto' => $item->gambar ? url($item->gambar) : null,
                'lokasi' => $item->lokasi,
                'author' => $item->user->name ?? 'Admin',
            ];
        });

        return Inertia::render('galeri', [
            'kegiatan' => $kegiatan
        ]);
    }

    public function addKegiatan(Request $request)
    {
        $request->validate([
            'nama_kegiatan' => 'required|string|max:255',
            'deskripsi' => 'required|string',
            'tanggal_kegiatan' => 'nullable|date',
            'jenis_kegiatan' => 'required|in:siswa,guru,umum',
            'lokasi' => 'nullable|string|max:255',
            'foto' => 'nullable|image|max:2048',
        ]);

        $data = [
            'judul_kegiatan' => $request->nama_kegiatan,
            'deskripsi_kegiatan' => $request->deskripsi,
            'tanggal_kegiatan' => $request->tanggal_kegiatan,
            'jenis_kegiatan' => $request->jenis_kegiatan,
            'lokasi' => $request->lokasi,
            'user_id' => Auth::id(),
        ];

        // Handle upload gambar
        if ($request->hasFile('foto')) {
            $filename = time() . '-' . $request->file('foto')->getClientOriginalName();
            $request->file('foto')->move(public_path('kegiatan'), $filename);
            $data['gambar'] = "kegiatan/{$filename}";
        }

        Kegiatan::create($data);

        return redirect()->back()->with('success', 'Kegiatan berhasil ditambahkan!');
    }

    public function updateKegiatan(Request $request, $id)
    {
        $kegiatan = Kegiatan::findOrFail($id);

        $request->validate([
            'nama_kegiatan' => 'required|string|max:255',
            'deskripsi' => 'required|string',
            'tanggal_kegiatan' => 'nullable|date',
            'jenis_kegiatan' => 'required|in:siswa,guru,umum',
            'lokasi' => 'nullable|string|max:255',
            'foto' => 'nullable|image|max:2048',
        ]);

        $data = [
            'judul_kegiatan' => $request->nama_kegiatan,
            'deskripsi_kegiatan' => $request->deskripsi,
            'tanggal_kegiatan' => $request->tanggal_kegiatan,
            'jenis_kegiatan' => $request->jenis_kegiatan,
            'lokasi' => $request->lokasi,
        ];

        // Handle upload gambar
        if ($request->hasFile('foto')) {
            // Hapus gambar lama jika ada
            if ($kegiatan->gambar) {
                $oldImagePath = public_path($kegiatan->gambar);
                if (file_exists($oldImagePath)) {
                    unlink($oldImagePath);
                }
            }

            $filename = time() . '-' . $request->file('foto')->getClientOriginalName();
            $request->file('foto')->move(public_path('kegiatan'), $filename);
            $data['gambar'] = "kegiatan/{$filename}";
        }

        $kegiatan->update($data);

        return redirect()->back()->with('success', 'Kegiatan berhasil diupdate!');
    }

    public function deleteKegiatan($id)
    {
        $kegiatan = Kegiatan::findOrFail($id);

        // Hapus file gambar dari folder
        if ($kegiatan->gambar) {
            $gambarPath = public_path($kegiatan->gambar);
            if (file_exists($gambarPath)) {
                unlink($gambarPath);
            }
        }

        $kegiatan->delete();

        return redirect()->back()->with('success', 'Kegiatan berhasil dihapus.');
    }

    public function galeriKegiatan()
    {
        $kegiatan = Kegiatan::with('user')->latest()->get()->map(function ($item) {
            return [
                'id' => $item->id,
                'nama_kegiatan' => $item->judul_kegiatan,
                'deskripsi' => $item->deskripsi_kegiatan,
                'tanggal_kegiatan' => Carbon::parse($item->tanggal_kegiatan)->format('d M Y'),
                'jenis_kegiatan' => $item->jenis_kegiatan,
                'foto' => $item->gambar ? url($item->gambar) : null,
                'lokasi' => $item->lokasi,
                'author' => $item->user->name ?? 'Admin',
                'created_at' => Carbon::parse($item->created_at)->format('d M Y'),
            ];
        });

        return Inertia::render('landingpage/page/page-kegiatan', [
            'kegiatan' => $kegiatan
        ]);
    }

}
