import { useState, useEffect, useRef } from 'react';
import { Link } from '@inertiajs/react';
import { motion, AnimatePresence } from 'framer-motion';
import {
    Menu,
    X,
    ChevronRight
} from 'lucide-react';

interface HeaderSectionProps {
    activeSection: string;
    scrollToSection: (sectionId: string) => void;
}

export default function HeaderSection({ activeSection, scrollToSection }: HeaderSectionProps) {
    const [menuOpen, setMenuOpen] = useState(false);
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const [digitalDropdownOpen, setDigitalDropdownOpen] = useState(false);
    const [galeriDropdownOpen, setGaleriDropdownOpen] = useState(false);
    const [mobileDigitalOpen, setMobileDigitalOpen] = useState(false);
    const [mobileLainnyaOpen, setMobileLainnyaOpen] = useState(false);
    const [mobileGaleriOpen, setMobileGaleriOpen] = useState(false);
    const menuRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
                setMenuOpen(false);
                setDropdownOpen(false);
                setDigitalDropdownOpen(false);
                setGaleriDropdownOpen(false);
            }
        }

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    useEffect(() => {
        function handleResize() {
            if (window.innerWidth >= 1024) {
                setMenuOpen(false);
            }
        }

        window.addEventListener('resize', handleResize);
        return () => {
            window.removeEventListener('resize', handleResize);
        };
    }, []);

    const handleScrollToSection = (sectionId: string) => {
        scrollToSection(sectionId);
        setMenuOpen(false);
    };

    return (
        <header className="sticky top-0 z-50 bg-gradient-to-r from-green-900 to-green-800 backdrop-blur-sm shadow-lg transition-all duration-300">
            <div className="mx-auto flex max-w-7xl items-center justify-between px-6 py-4" ref={menuRef}>
                <motion.div
                    className="flex items-center gap-3"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5 }}
                >
                    <img
                        src="/assets/logo_mts.png"
                        alt="Logo Sekolah"
                        className="h-10 w-10 object-cover rounded-full"
                    />
                    <div>
                        <h1 className="text-xl font-bold text-white">MTs Negeri 4 Gunungkidul</h1>
                        <p className="text-xs text-white/80">Excellence in Education</p>
                    </div>
                </motion.div>

                <div className="lg:hidden">
                    <button
                        onClick={(e) => {
                            e.stopPropagation();
                            setMenuOpen(!menuOpen);
                        }}
                        className="p-2 rounded-lg hover:bg-green-800/30 text-white transition-colors focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-offset-2 focus:ring-offset-green-700"
                    >
                        {menuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
                    </button>
                </div>

                <nav className="hidden lg:flex items-center gap-8 text-sm font-medium text-white">
                    {["Beranda","Tentang", "Berita", "Prestasi"].map((menu) => {
                        const isActive = activeSection === menu.toLowerCase();
                        return (
                            <button
                                key={menu}
                                onClick={() => handleScrollToSection(menu.toLowerCase())}
                                className={`hover:text-yellow-300 relative group transition-colors duration-200 ${isActive ? 'text-yellow-300' : ''}`}
                            >
                                {menu}
                                <span className={`absolute -bottom-1 left-0 h-0.5 bg-yellow-300 transition-all duration-300 ${isActive ? 'w-full' : 'w-0 group-hover:w-full'}`}></span>
                            </button>
                        );
                    })}
                    <div className="relative">
                        <button
                            onClick={() => setGaleriDropdownOpen(!galeriDropdownOpen)}
                            className={`flex items-center gap-1 hover:text-yellow-300 relative group transition-colors duration-200 ${activeSection === 'galeri' ? 'text-yellow-300' : ''}`}
                        >
                            Galeri <ChevronRight className={`w-4 h-4 transition-transform ${galeriDropdownOpen ? 'rotate-90' : ''}`} />
                            <span className={`absolute -bottom-1 left-0 h-0.5 bg-yellow-300 transition-all duration-300 ${activeSection === 'galeri' ? 'w-full' : 'w-0 group-hover:w-full'}`}></span>
                        </button>
                        <AnimatePresence>
                            {galeriDropdownOpen && (
                                <motion.div
                                    className="absolute left-0 mt-2 w-56 bg-white text-gray-700 rounded shadow-xl z-50"
                                    initial={{ opacity: 0, y: -10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    exit={{ opacity: 0, y: -10 }}
                                    transition={{ duration: 0.2 }}
                                >
                                    <button
                                        onClick={() => {
                                            handleScrollToSection('galeri');
                                            setGaleriDropdownOpen(false);
                                        }}
                                        className="block w-full text-left px-4 py-2 hover:bg-gray-100"
                                    >
                                        Kegiatan Guru dan Siswa
                                    </button>
                                    <button
                                        onClick={() => {
                                            handleScrollToSection('galeri');
                                            setGaleriDropdownOpen(false);
                                        }}
                                        className="block w-full text-left px-4 py-2 hover:bg-gray-100"
                                    >
                                        Karya Siswa
                                    </button>
                                </motion.div>
                            )}
                        </AnimatePresence>
                    </div>
                    {["Kontak"].map((menu) => {
                        const isActive = activeSection === menu.toLowerCase();
                        return (
                            <button
                                key={menu}
                                onClick={() => handleScrollToSection(menu.toLowerCase())}
                                className={`hover:text-yellow-300 relative group transition-colors duration-200 ${isActive ? 'text-yellow-300' : ''}`}
                            >
                                {menu}
                                <span className={`absolute -bottom-1 left-0 h-0.5 bg-yellow-300 transition-all duration-300 ${isActive ? 'w-full' : 'w-0 group-hover:w-full'}`}></span>
                            </button>
                        );
                    })}
                    <a href="https://ppdb.mtsn4gk.sch.id/" target="_blank" rel="noopener noreferrer" className="hover:text-yellow-300 relative group transition-colors duration-200">
                        PPDB
                        <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-yellow-300 transition-all duration-300 group-hover:w-full"></span>
                    </a>
                    <div className="relative">
                        <button
                            onClick={() => setDigitalDropdownOpen(!digitalDropdownOpen)}
                            className="flex items-center gap-1 hover:text-yellow-300 transition"
                        >
                            Digital <ChevronRight className={`w-4 h-4 transition-transform ${digitalDropdownOpen ? 'rotate-90' : ''}`} />
                        </button>
                        <AnimatePresence>
                            {digitalDropdownOpen && (
                                <motion.div
                                    className="absolute right-0 mt-2 w-48 bg-white text-gray-700 rounded shadow-xl z-50"
                                    initial={{ opacity: 0, y: -10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    exit={{ opacity: 0, y: -10 }}
                                    transition={{ duration: 0.2 }}
                                >
                                    <button
                                        onClick={() => {
                                            alert('Modul Pembelajaran akan segera tersedia!');
                                            setDigitalDropdownOpen(false);
                                        }}
                                        className="block w-full text-left px-4 py-2 hover:bg-gray-100"
                                    >
                                        Modul Pembelajaran
                                    </button>
                                </motion.div>
                            )}
                        </AnimatePresence>
                    </div>
                    <div className="relative">
                        <button
                            onClick={() => setDropdownOpen(!dropdownOpen)}
                            className="flex items-center gap-1 hover:text-yellow-300 transition"
                        >
                            Lain-lain <ChevronRight className={`w-4 h-4 transition-transform ${dropdownOpen ? 'rotate-90' : ''}`} />
                        </button>
                        <AnimatePresence>
                            {dropdownOpen && (
                                <motion.div
                                    className="absolute right-0 mt-2 w-48 bg-white text-gray-700 rounded shadow-xl z-50"
                                    initial={{ opacity: 0, y: -10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    exit={{ opacity: 0, y: -10 }}
                                    transition={{ duration: 0.2 }}
                                >
                                    <Link
                                        href="/login"
                                        className="block px-4 py-2 hover:bg-gray-100"
                                        onClick={() => setDropdownOpen(false)}
                                    >
                                        Login Admin
                                    </Link>
                                </motion.div>
                            )}
                        </AnimatePresence>
                    </div>
                </nav>
            </div>

            <AnimatePresence>
                {menuOpen && (
                    <motion.nav
                        className="lg:hidden flex flex-col gap-2 px-6 py-4 bg-green-900 text-white"
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3 }}
                    >
                        {["Beranda","Tentang", "Berita", "Prestasi"].map((menu) => {
                            const isActive = activeSection === menu.toLowerCase();
                            return (
                                <button
                                    key={menu}
                                    onClick={() => handleScrollToSection(menu.toLowerCase())}
                                    className={`text-left w-full py-2 hover:text-yellow-300 ${isActive ? 'text-yellow-300 font-semibold' : ''}`}
                                >
                                    {menu}
                                </button>
                            );
                        })}

                        <div>
                            <button
                                onClick={() => setMobileGaleriOpen(!mobileGaleriOpen)}
                                className={`flex items-center justify-between w-full py-2 hover:text-yellow-300 ${activeSection === 'galeri' ? 'text-yellow-300 font-semibold' : ''}`}
                            >
                                Galeri
                                <ChevronRight className={`w-4 h-4 transition-transform ${mobileGaleriOpen ? 'rotate-90' : ''}`} />
                            </button>
                            <AnimatePresence>
                                {mobileGaleriOpen && (
                                    <motion.div
                                        initial={{ opacity: 0, height: 0 }}
                                        animate={{ opacity: 1, height: 'auto' }}
                                        exit={{ opacity: 0, height: 0 }}
                                        className="pl-4"
                                    >
                                        <button
                                            onClick={() => {
                                                handleScrollToSection('galeri');
                                                setMenuOpen(false);
                                            }}
                                            className="block w-full text-left py-2 hover:text-yellow-300"
                                        >
                                            Kegiatan Guru dan Siswa
                                        </button>
                                        <button
                                            onClick={() => {
                                                handleScrollToSection('galeri');
                                                setMenuOpen(false);
                                            }}
                                            className="block w-full text-left py-2 hover:text-yellow-300"
                                        >
                                            Karya Siswa
                                        </button>
                                    </motion.div>
                                )}
                            </AnimatePresence>
                        </div>

                        {["Kontak"].map((menu) => {
                            const isActive = activeSection === menu.toLowerCase();
                            return (
                                <button
                                    key={menu}
                                    onClick={() => handleScrollToSection(menu.toLowerCase())}
                                    className={`text-left w-full py-2 hover:text-yellow-300 ${isActive ? 'text-yellow-300 font-semibold' : ''}`}
                                >
                                    {menu}
                                </button>
                            );
                        })}
                        <a
                            href="https://ppdb.mtsn4gk.sch.id/"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="block py-2 hover:text-yellow-300"
                            onClick={() => setMenuOpen(false)}
                        >
                            PPDB
                        </a>
                        
                        <div>
                            <button
                                onClick={() => setMobileDigitalOpen(!mobileDigitalOpen)}
                                className="flex items-center justify-between w-full py-2 hover:text-yellow-300"
                            >
                                Digital
                                <ChevronRight className={`w-4 h-4 transition-transform ${mobileDigitalOpen ? 'rotate-90' : ''}`} />
                            </button>
                            <AnimatePresence>
                                {mobileDigitalOpen && (
                                    <motion.div
                                        initial={{ opacity: 0, height: 0 }}
                                        animate={{ opacity: 1, height: 'auto' }}
                                        exit={{ opacity: 0, height: 0 }}
                                        className="pl-4"
                                    >
                                        <button
                                            onClick={() => {
                                                alert('Modul Pembelajaran akan segera tersedia!');
                                                setMenuOpen(false);
                                            }}
                                            className="block w-full text-left py-2 hover:text-yellow-300"
                                        >
                                            Modul Pembelajaran
                                        </button>
                                    </motion.div>
                                )}
                            </AnimatePresence>
                        </div>

                        <div>
                            <button
                                onClick={() => setMobileLainnyaOpen(!mobileLainnyaOpen)}
                                className="flex items-center justify-between w-full py-2 hover:text-yellow-300"
                            >
                                Lain-lain
                                <ChevronRight className={`w-4 h-4 transition-transform ${mobileLainnyaOpen ? 'rotate-90' : ''}`} />
                            </button>
                            <AnimatePresence>
                                {mobileLainnyaOpen && (
                                    <motion.div
                                        initial={{ opacity: 0, height: 0 }}
                                        animate={{ opacity: 1, height: 'auto' }}
                                        exit={{ opacity: 0, height: 0 }}
                                        className="pl-4"
                                    >
                                        <Link
                                            href="/login"
                                            className="block py-2 hover:text-yellow-300"
                                            onClick={() => setMenuOpen(false)}
                                        >
                                            Login Admin
                                        </Link>
                                    </motion.div>
                                )}
                            </AnimatePresence>
                        </div>
                    </motion.nav>
                )}
            </AnimatePresence>
        </header>
    );
}